"""
LangChain service for AI operations
"""
from typing import Dict, List, Optional, Any
from langchain_anthropic import <PERSON>t<PERSON>nthropic
from langchain.schema import HumanMessage, SystemMessage
from langchain.prompts import ChatPromptTemplate, PromptTemplate
from langchain.output_parsers import PydanticOutputParser, OutputFixingParser
from langchain.text_splitter import RecursiveCharacterTextSplitter
from pydantic import BaseModel, Field
import json
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class ResumeData(BaseModel):
    """Structured resume data model"""
    full_name: Optional[str] = Field(description="Full name of the person")
    email: Optional[str] = Field(description="Email address")
    phone: Optional[str] = Field(description="Phone number")
    location: Optional[str] = Field(description="Location/address")
    summary: Optional[str] = Field(description="Professional summary or objective")
    skills: List[str] = Field(default=[], description="List of skills")
    experience: List[Dict[str, Any]] = Field(default=[], description="Work experience entries")
    education: List[Dict[str, Any]] = Field(default=[], description="Education entries")
    certifications: List[Dict[str, Any]] = Field(default=[], description="Certifications")
    languages: List[str] = Field(default=[], description="Languages spoken")

class JobAnalysis(BaseModel):
    """Job analysis model"""
    required_skills: List[str] = Field(default=[], description="Required skills")
    preferred_skills: List[str] = Field(default=[], description="Preferred skills")
    experience_level: Optional[str] = Field(description="Required experience level")
    education_requirements: Optional[str] = Field(description="Education requirements")
    key_responsibilities: List[str] = Field(default=[], description="Key job responsibilities")
    company_culture: Optional[str] = Field(description="Company culture insights")

class MatchScore(BaseModel):
    """Job-resume match score model"""
    overall_score: float = Field(description="Overall match score (0-1)")
    skills_score: float = Field(description="Skills match score (0-1)")
    experience_score: float = Field(description="Experience match score (0-1)")
    education_score: float = Field(description="Education match score (0-1)")
    reasons: List[str] = Field(default=[], description="Reasons for the match score")

class LangChainService:
    """Service for LangChain operations"""
    
    def __init__(self):
        self.llm = ChatAnthropic(
            anthropic_api_key=settings.ANTHROPIC_API_KEY,
            model="claude-3-sonnet-20240229",
            temperature=0.1,
            max_tokens=4000
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=2000,
            chunk_overlap=200
        )
    
    async def parse_resume(self, resume_text: str) -> ResumeData:
        """Parse resume text and extract structured data"""
        try:
            parser = PydanticOutputParser(pydantic_object=ResumeData)
            fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=self.llm)
            
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert resume parser. Extract structured information from the resume text.
                Be thorough and accurate. For experience and education, include:
                - title/degree, company/institution, dates, description/achievements
                
                {format_instructions}"""),
                ("human", "Resume text:\n{resume_text}")
            ])
            
            chain = prompt | self.llm | fixing_parser
            
            result = await chain.ainvoke({
                "resume_text": resume_text,
                "format_instructions": parser.get_format_instructions()
            })
            
            logger.info("Resume parsed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error parsing resume: {e}")
            raise
    
    async def analyze_job(self, job_description: str) -> JobAnalysis:
        """Analyze job description and extract requirements"""
        try:
            parser = PydanticOutputParser(pydantic_object=JobAnalysis)
            fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=self.llm)
            
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert job analyst. Extract key requirements and information from job descriptions.
                Focus on skills, experience, education, and company culture insights.
                
                {format_instructions}"""),
                ("human", "Job description:\n{job_description}")
            ])
            
            chain = prompt | self.llm | fixing_parser
            
            result = await chain.ainvoke({
                "job_description": job_description,
                "format_instructions": parser.get_format_instructions()
            })
            
            logger.info("Job analyzed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing job: {e}")
            raise
    
    async def calculate_match_score(self, resume_data: ResumeData, job_analysis: JobAnalysis) -> MatchScore:
        """Calculate match score between resume and job"""
        try:
            parser = PydanticOutputParser(pydantic_object=MatchScore)
            fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=self.llm)
            
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert job matching analyst. Calculate detailed match scores between a resume and job requirements.
                
                Scoring criteria:
                - Skills: How well do the candidate's skills match required/preferred skills?
                - Experience: Does the experience level and type match requirements?
                - Education: Does education meet the requirements?
                - Overall: Weighted combination considering job importance
                
                Provide scores from 0.0 to 1.0 and detailed reasons.
                
                {format_instructions}"""),
                ("human", """Resume Data:
                {resume_data}
                
                Job Requirements:
                {job_analysis}""")
            ])
            
            chain = prompt | self.llm | fixing_parser
            
            result = await chain.ainvoke({
                "resume_data": resume_data.json(),
                "job_analysis": job_analysis.json(),
                "format_instructions": parser.get_format_instructions()
            })
            
            logger.info("Match score calculated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating match score: {e}")
            raise
    
    async def customize_resume(self, resume_data: ResumeData, job_analysis: JobAnalysis) -> str:
        """Customize resume content for specific job"""
        try:
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert resume writer. Customize the resume to better match the job requirements.
                
                Guidelines:
                - Highlight relevant skills and experience
                - Adjust summary to match job requirements
                - Reorder and emphasize relevant experience
                - Use keywords from job description
                - Maintain truthfulness - don't add false information
                - Keep the same format and structure
                
                Return the customized resume in a professional format."""),
                ("human", """Original Resume Data:
                {resume_data}
                
                Job Requirements:
                {job_analysis}
                
                Please provide a customized resume that better matches this job.""")
            ])
            
            chain = prompt | self.llm
            
            result = await chain.ainvoke({
                "resume_data": resume_data.json(),
                "job_analysis": job_analysis.json()
            })
            
            logger.info("Resume customized successfully")
            return result.content
            
        except Exception as e:
            logger.error(f"Error customizing resume: {e}")
            raise
    
    async def generate_cover_letter(self, resume_data: ResumeData, job_analysis: JobAnalysis, 
                                  company_name: str, job_title: str) -> str:
        """Generate personalized cover letter"""
        try:
            prompt = ChatPromptTemplate.from_messages([
                ("system", """You are an expert cover letter writer. Create a compelling, personalized cover letter.
                
                Guidelines:
                - Professional and engaging tone
                - Highlight relevant experience and skills
                - Show enthusiasm for the role and company
                - Address specific job requirements
                - Keep it concise (3-4 paragraphs)
                - Include proper formatting
                
                Structure:
                1. Opening: Express interest and briefly introduce yourself
                2. Body: Highlight relevant experience and skills
                3. Closing: Express enthusiasm and next steps"""),
                ("human", """Candidate Information:
                {resume_data}
                
                Job Information:
                Company: {company_name}
                Position: {job_title}
                Requirements: {job_analysis}
                
                Please write a personalized cover letter for this application.""")
            ])
            
            chain = prompt | self.llm
            
            result = await chain.ainvoke({
                "resume_data": resume_data.json(),
                "job_analysis": job_analysis.json(),
                "company_name": company_name,
                "job_title": job_title
            })
            
            logger.info("Cover letter generated successfully")
            return result.content
            
        except Exception as e:
            logger.error(f"Error generating cover letter: {e}")
            raise

# Global service instance
langchain_service = LangChainService()
