"""
Document processing service for extracting text from various file formats
"""
import os
import magic
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import tempfile

# PDF processing
import PyPDF2
import pdfplumber
import pymupdf as fitz

# Word document processing
from docx import Document
import docx2txt

# LangChain document loaders
from langchain.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    UnstructuredPDFLoader,
    UnstructuredWordDocumentLoader
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document as LangChainDocument

from app.core.config import settings

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Service for processing and extracting text from documents"""
    
    SUPPORTED_MIME_TYPES = {
        'application/pdf': 'pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'application/msword': 'doc',
        'text/plain': 'txt'
    }
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """Validate uploaded file and return file info"""
        try:
            if not os.path.exists(file_path):
                raise ValueError("File does not exist")
            
            # Get file size
            file_size = os.path.getsize(file_path)
            if file_size > settings.MAX_FILE_SIZE:
                raise ValueError(f"File size ({file_size}) exceeds maximum allowed size ({settings.MAX_FILE_SIZE})")
            
            # Detect MIME type
            mime_type = magic.from_file(file_path, mime=True)
            
            if mime_type not in self.SUPPORTED_MIME_TYPES:
                raise ValueError(f"Unsupported file type: {mime_type}")
            
            file_type = self.SUPPORTED_MIME_TYPES[mime_type]
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'mime_type': mime_type,
                'file_type': file_type,
                'is_valid': True
            }
            
        except Exception as e:
            logger.error(f"File validation failed: {e}")
            return {
                'file_path': file_path,
                'is_valid': False,
                'error': str(e)
            }
    
    def extract_text_pdf(self, file_path: str) -> str:
        """Extract text from PDF using multiple methods for best results"""
        text = ""
        
        try:
            # Method 1: Try pdfplumber first (best for complex layouts)
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            if text.strip():
                logger.info(f"Successfully extracted text using pdfplumber: {len(text)} characters")
                return text.strip()
                
        except Exception as e:
            logger.warning(f"pdfplumber failed: {e}")
        
        try:
            # Method 2: Try PyMuPDF (good for most PDFs)
            doc = fitz.open(file_path)
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text() + "\n"
            doc.close()
            
            if text.strip():
                logger.info(f"Successfully extracted text using PyMuPDF: {len(text)} characters")
                return text.strip()
                
        except Exception as e:
            logger.warning(f"PyMuPDF failed: {e}")
        
        try:
            # Method 3: Fallback to PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            
            if text.strip():
                logger.info(f"Successfully extracted text using PyPDF2: {len(text)} characters")
                return text.strip()
                
        except Exception as e:
            logger.error(f"All PDF extraction methods failed: {e}")
            raise ValueError("Could not extract text from PDF")
        
        if not text.strip():
            raise ValueError("No text could be extracted from PDF")
        
        return text.strip()
    
    def extract_text_docx(self, file_path: str) -> str:
        """Extract text from DOCX files"""
        try:
            # Method 1: Try docx2txt (simpler and often more reliable)
            text = docx2txt.process(file_path)
            
            if text and text.strip():
                logger.info(f"Successfully extracted text using docx2txt: {len(text)} characters")
                return text.strip()
            
        except Exception as e:
            logger.warning(f"docx2txt failed: {e}")
        
        try:
            # Method 2: Fallback to python-docx
            doc = Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            if text.strip():
                logger.info(f"Successfully extracted text using python-docx: {len(text)} characters")
                return text.strip()
            else:
                raise ValueError("No text found in DOCX file")
                
        except Exception as e:
            logger.error(f"DOCX extraction failed: {e}")
            raise ValueError("Could not extract text from DOCX file")
    
    def extract_text_doc(self, file_path: str) -> str:
        """Extract text from DOC files (legacy Word format)"""
        try:
            # For DOC files, we'll try to use python-docx
            # Note: This might not work for all DOC files
            doc = Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            if text.strip():
                logger.info(f"Successfully extracted text from DOC: {len(text)} characters")
                return text.strip()
            else:
                raise ValueError("No text found in DOC file")
                
        except Exception as e:
            logger.error(f"DOC extraction failed: {e}")
            raise ValueError("Could not extract text from DOC file. Consider converting to DOCX format.")
    
    def extract_text_txt(self, file_path: str) -> str:
        """Extract text from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            logger.info(f"Successfully extracted text from TXT: {len(text)} characters")
            return text.strip()
            
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    text = file.read()
                logger.info(f"Successfully extracted text from TXT (latin-1): {len(text)} characters")
                return text.strip()
            except Exception as e:
                logger.error(f"TXT extraction failed: {e}")
                raise ValueError("Could not extract text from TXT file")
        except Exception as e:
            logger.error(f"TXT extraction failed: {e}")
            raise ValueError("Could not extract text from TXT file")
    
    def extract_text(self, file_path: str, file_type: Optional[str] = None) -> str:
        """Main method to extract text from any supported file type"""
        if not file_type:
            file_info = self.validate_file(file_path)
            if not file_info['is_valid']:
                raise ValueError(file_info['error'])
            file_type = file_info['file_type']
        
        extraction_methods = {
            'pdf': self.extract_text_pdf,
            'docx': self.extract_text_docx,
            'doc': self.extract_text_doc,
            'txt': self.extract_text_txt
        }
        
        if file_type not in extraction_methods:
            raise ValueError(f"Unsupported file type: {file_type}")
        
        try:
            text = extraction_methods[file_type](file_path)
            
            if not text or len(text.strip()) < 10:
                raise ValueError("Extracted text is too short or empty")
            
            logger.info(f"Successfully extracted {len(text)} characters from {file_type} file")
            return text
            
        except Exception as e:
            logger.error(f"Text extraction failed for {file_type}: {e}")
            raise
    
    def create_langchain_documents(self, file_path: str, file_type: Optional[str] = None) -> list[LangChainDocument]:
        """Create LangChain documents from file using appropriate loaders"""
        try:
            if not file_type:
                file_info = self.validate_file(file_path)
                if not file_info['is_valid']:
                    raise ValueError(file_info['error'])
                file_type = file_info['file_type']
            
            # Choose appropriate LangChain loader
            if file_type == 'pdf':
                # Try UnstructuredPDFLoader first for better formatting
                try:
                    loader = UnstructuredPDFLoader(file_path)
                    documents = loader.load()
                except Exception:
                    # Fallback to PyPDFLoader
                    loader = PyPDFLoader(file_path)
                    documents = loader.load()
                    
            elif file_type == 'docx':
                try:
                    loader = UnstructuredWordDocumentLoader(file_path)
                    documents = loader.load()
                except Exception:
                    # Fallback to Docx2txtLoader
                    loader = Docx2txtLoader(file_path)
                    documents = loader.load()
                    
            elif file_type in ['doc', 'txt']:
                loader = TextLoader(file_path, encoding='utf-8')
                documents = loader.load()
            else:
                raise ValueError(f"Unsupported file type for LangChain: {file_type}")
            
            # Split documents into chunks
            split_documents = self.text_splitter.split_documents(documents)
            
            logger.info(f"Created {len(split_documents)} LangChain document chunks")
            return split_documents
            
        except Exception as e:
            logger.error(f"LangChain document creation failed: {e}")
            raise
    
    def process_document(self, file_path: str) -> Dict[str, Any]:
        """Complete document processing pipeline"""
        try:
            # Validate file
            file_info = self.validate_file(file_path)
            if not file_info['is_valid']:
                raise ValueError(file_info['error'])
            
            # Extract text
            raw_text = self.extract_text(file_path, file_info['file_type'])
            
            # Create LangChain documents
            langchain_docs = self.create_langchain_documents(file_path, file_info['file_type'])
            
            return {
                'success': True,
                'file_info': file_info,
                'raw_text': raw_text,
                'text_length': len(raw_text),
                'langchain_documents': langchain_docs,
                'chunk_count': len(langchain_docs)
            }
            
        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }

# Global service instance
document_processor = DocumentProcessor()
