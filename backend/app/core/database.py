"""
Database connection and configuration
"""
from prisma import Prisma
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Global database instance
db = Prisma()

async def connect_db():
    """Connect to the database"""
    try:
        await db.connect()
        logger.info("Database connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise

async def disconnect_db():
    """Disconnect from the database"""
    try:
        await db.disconnect()
        logger.info("Database disconnected successfully")
    except Exception as e:
        logger.error(f"Failed to disconnect from database: {e}")
        raise

async def get_db():
    """Dependency to get database instance"""
    return db
