"""
File handling utilities
"""
import os
import uuid
import shutil
import mimetypes
from typing import Optional, Dict, Any, List
from pathlib import Path
import magic
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class FileManager:
    """Utility class for file operations"""
    
    ALLOWED_EXTENSIONS = {
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.doc': 'application/msword',
        '.txt': 'text/plain'
    }
    
    @staticmethod
    def validate_file_extension(filename: str) -> bool:
        """Validate file extension"""
        ext = Path(filename).suffix.lower()
        return ext in FileManager.ALLOWED_EXTENSIONS
    
    @staticmethod
    def get_mime_type(file_path: str) -> str:
        """Get MIME type of file"""
        try:
            # Use python-magic for accurate detection
            mime_type = magic.from_file(file_path, mime=True)
            return mime_type
        except Exception:
            # Fallback to mimetypes module
            mime_type, _ = mimetypes.guess_type(file_path)
            return mime_type or 'application/octet-stream'
    
    @staticmethod
    def generate_unique_filename(original_filename: str, prefix: str = "") -> str:
        """Generate unique filename while preserving extension"""
        ext = Path(original_filename).suffix
        unique_id = str(uuid.uuid4())
        if prefix:
            return f"{prefix}_{unique_id}{ext}"
        return f"{unique_id}{ext}"
    
    @staticmethod
    def ensure_directory(directory: str) -> Path:
        """Ensure directory exists and return Path object"""
        path = Path(directory)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def save_uploaded_file(file_content: bytes, filename: str, subdirectory: str = "") -> str:
        """Save uploaded file and return file path"""
        try:
            # Create directory structure
            if subdirectory:
                upload_dir = Path(settings.UPLOAD_DIR) / subdirectory
            else:
                upload_dir = Path(settings.UPLOAD_DIR)
            
            FileManager.ensure_directory(upload_dir)
            
            # Generate unique filename
            unique_filename = FileManager.generate_unique_filename(filename)
            file_path = upload_dir / unique_filename
            
            # Save file
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"File saved: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save file: {e}")
            raise
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """Delete file safely"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """Get comprehensive file information"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = os.stat(file_path)
            path_obj = Path(file_path)
            
            return {
                'path': file_path,
                'filename': path_obj.name,
                'extension': path_obj.suffix,
                'size': stat.st_size,
                'mime_type': FileManager.get_mime_type(file_path),
                'created_at': stat.st_ctime,
                'modified_at': stat.st_mtime,
                'is_readable': os.access(file_path, os.R_OK),
                'is_writable': os.access(file_path, os.W_OK)
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            raise
    
    @staticmethod
    def validate_file_size(file_path: str, max_size: Optional[int] = None) -> bool:
        """Validate file size"""
        if max_size is None:
            max_size = settings.MAX_FILE_SIZE
        
        try:
            file_size = os.path.getsize(file_path)
            return file_size <= max_size
        except Exception:
            return False
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """Clean filename by removing invalid characters"""
        import re
        # Remove invalid characters
        cleaned = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove multiple underscores
        cleaned = re.sub(r'_+', '_', cleaned)
        # Remove leading/trailing underscores and dots
        cleaned = cleaned.strip('_.')
        return cleaned or 'unnamed_file'
    
    @staticmethod
    def copy_file(source: str, destination: str) -> bool:
        """Copy file from source to destination"""
        try:
            # Ensure destination directory exists
            dest_dir = Path(destination).parent
            FileManager.ensure_directory(dest_dir)
            
            shutil.copy2(source, destination)
            logger.info(f"File copied from {source} to {destination}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to copy file: {e}")
            return False
    
    @staticmethod
    def move_file(source: str, destination: str) -> bool:
        """Move file from source to destination"""
        try:
            # Ensure destination directory exists
            dest_dir = Path(destination).parent
            FileManager.ensure_directory(dest_dir)
            
            shutil.move(source, destination)
            logger.info(f"File moved from {source} to {destination}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to move file: {e}")
            return False
    
    @staticmethod
    def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
        """List files in directory matching pattern"""
        try:
            path = Path(directory)
            if not path.exists():
                return []
            
            if recursive:
                files = list(path.rglob(pattern))
            else:
                files = list(path.glob(pattern))
            
            return [str(f) for f in files if f.is_file()]
            
        except Exception as e:
            logger.error(f"Failed to list files: {e}")
            return []
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """Get total size of directory"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
            return total_size
        except Exception as e:
            logger.error(f"Failed to get directory size: {e}")
            return 0
    
    @staticmethod
    def cleanup_old_files(directory: str, max_age_days: int = 30) -> int:
        """Clean up files older than specified days"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60
            deleted_count = 0
            
            for file_path in FileManager.list_files(directory, recursive=True):
                try:
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"Deleted old file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete old file {file_path}: {e}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old files: {e}")
            return 0

# Global file manager instance
file_manager = FileManager()
