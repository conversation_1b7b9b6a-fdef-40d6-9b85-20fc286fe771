"""
Resume Pydantic models
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

class ResumeBase(BaseModel):
    """Base resume model"""
    fileName: str = Field(..., description="Original filename")
    fullName: Optional[str] = Field(None, description="Full name extracted from resume")
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    location: Optional[str] = Field(None, description="Location/address")
    summary: Optional[str] = Field(None, description="Professional summary")
    skills: List[str] = Field(default=[], description="List of skills")
    experience: List[Dict[str, Any]] = Field(default=[], description="Work experience")
    education: List[Dict[str, Any]] = Field(default=[], description="Education history")
    certifications: List[Dict[str, Any]] = Field(default=[], description="Certifications")
    languages: List[str] = Field(default=[], description="Languages")

class ResumeCreate(ResumeBase):
    """Model for creating a new resume"""
    userId: str = Field(..., description="User ID who owns the resume")
    filePath: str = Field(..., description="Path to uploaded file")
    fileSize: int = Field(..., description="File size in bytes")
    mimeType: str = Field(..., description="MIME type of the file")
    rawText: Optional[str] = Field(None, description="Raw extracted text")
    
    @validator('fileSize')
    def validate_file_size(cls, v):
        if v <= 0:
            raise ValueError('File size must be positive')
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v

class ResumeUpdate(BaseModel):
    """Model for updating resume data"""
    fullName: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    summary: Optional[str] = None
    skills: Optional[List[str]] = None
    experience: Optional[List[Dict[str, Any]]] = None
    education: Optional[List[Dict[str, Any]]] = None
    certifications: Optional[List[Dict[str, Any]]] = None
    languages: Optional[List[str]] = None
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v

class ResumeResponse(ResumeBase):
    """Model for resume API responses"""
    id: str = Field(..., description="Resume ID")
    fileSize: int = Field(..., description="File size in bytes")
    mimeType: str = Field(..., description="MIME type")
    textLength: int = Field(0, description="Length of extracted text")
    chunkCount: Optional[int] = Field(None, description="Number of document chunks")
    createdAt: datetime = Field(..., description="Creation timestamp")
    updatedAt: Optional[datetime] = Field(None, description="Last update timestamp")
    isProcessed: bool = Field(False, description="Whether AI processing completed")
    
    class Config:
        from_attributes = True

class ResumeListResponse(BaseModel):
    """Model for paginated resume list"""
    resumes: List[ResumeResponse]
    total: int
    skip: int
    limit: int

class DocumentProcessingStatus(BaseModel):
    """Model for document processing status"""
    status: str = Field(..., description="Processing status: pending, processing, completed, failed")
    message: Optional[str] = Field(None, description="Status message")
    progress: Optional[float] = Field(None, description="Processing progress (0-1)")
    error: Optional[str] = Field(None, description="Error message if failed")

class ResumeAnalysis(BaseModel):
    """Model for resume analysis results"""
    resumeId: str
    strengths: List[str] = Field(default=[], description="Resume strengths")
    weaknesses: List[str] = Field(default=[], description="Areas for improvement")
    suggestions: List[str] = Field(default=[], description="Improvement suggestions")
    skillsGaps: List[str] = Field(default=[], description="Missing skills for target roles")
    overallScore: float = Field(0.0, description="Overall resume score (0-1)")
    
class CustomizedResumeRequest(BaseModel):
    """Model for resume customization request"""
    resumeId: str = Field(..., description="Source resume ID")
    jobId: str = Field(..., description="Target job ID")
    customizations: Optional[Dict[str, Any]] = Field(None, description="Custom modifications")
    
class CustomizedResumeResponse(BaseModel):
    """Model for customized resume response"""
    id: str = Field(..., description="Customized resume ID")
    resumeId: str = Field(..., description="Source resume ID")
    jobId: str = Field(..., description="Target job ID")
    fileName: str = Field(..., description="Generated file name")
    filePath: str = Field(..., description="Path to generated file")
    content: Dict[str, Any] = Field(..., description="Customized content")
    createdAt: datetime = Field(..., description="Creation timestamp")
    
    class Config:
        from_attributes = True
