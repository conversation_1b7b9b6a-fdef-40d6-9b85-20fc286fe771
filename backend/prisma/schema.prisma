// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-py"
  interface = "asyncio"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  firstName String?
  lastName  String?
  password  String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  resumes      Resume[]
  applications Application[]
  jobMatches   JobMatch[]

  @@map("users")
}

model Resume {
  id          String   @id @default(cuid())
  userId      String
  fileName    String
  filePath    String
  fileSize    Int
  mimeType    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Parsed resume data
  fullName     String?
  email        String?
  phone        String?
  location     String?
  summary      String?
  skills       String[] // Array of skills
  experience   Json?    // JSON structure for work experience
  education    Json?    // JSON structure for education
  certifications Json?  // JSON structure for certifications
  languages    String[] // Array of languages
  rawText      String?  // Full extracted text

  // Relations
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  applications Application[]
  jobMatches   JobMatch[]
  customizedResumes CustomizedResume[]

  @@map("resumes")
}

model Job {
  id              String   @id @default(cuid())
  externalId      String?  // ID from job board
  site            String   // linkedin, indeed, etc.
  title           String
  company         String
  location        String?
  description     String?
  jobType         String?  // fulltime, parttime, etc.
  salaryMin       Float?
  salaryMax       Float?
  salaryInterval  String?  // yearly, monthly, hourly
  currency        String?  @default("USD")
  isRemote        Boolean? @default(false)
  datePosted      DateTime?
  jobUrl          String
  jobUrlDirect    String?
  companyUrl      String?
  skills          String[] // Required skills
  experience      String?  // Experience requirements
  education       String?  // Education requirements
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  applications Application[]
  jobMatches   JobMatch[]

  @@unique([externalId, site])
  @@map("jobs")
}

model JobMatch {
  id          String   @id @default(cuid())
  userId      String
  resumeId    String
  jobId       String
  matchScore  Float    // 0.0 to 1.0
  skillsMatch Float?   // Skills matching score
  expMatch    Float?   // Experience matching score
  eduMatch    Float?   // Education matching score
  reasons     String[] // Reasons for the match
  isBookmarked Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  job    Job    @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@unique([userId, resumeId, jobId])
  @@map("job_matches")
}

model Application {
  id            String            @id @default(cuid())
  userId        String
  resumeId      String
  jobId         String
  status        ApplicationStatus @default(PENDING)
  appliedAt     DateTime?
  responseAt    DateTime?
  followUpAt    DateTime?
  notes         String?
  coverLetter   String?
  customResume  String?           // Path to customized resume
  isAutomated   Boolean           @default(false)
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  job    Job    @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@map("applications")
}

model CustomizedResume {
  id        String   @id @default(cuid())
  resumeId  String
  jobId     String
  fileName  String
  filePath  String
  content   Json     // Customized resume content
  createdAt DateTime @default(now())

  // Relations
  resume Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  @@map("customized_resumes")
}

model CoverLetter {
  id           String   @id @default(cuid())
  applicationId String?
  jobId        String
  content      String
  template     String?  // Template used
  createdAt    DateTime @default(now())

  @@map("cover_letters")
}

model JobSearch {
  id          String   @id @default(cuid())
  userId      String
  searchTerm  String
  location    String?
  jobType     String?
  salaryMin   Float?
  salaryMax   Float?
  isRemote    Boolean?
  sites       String[] // Which sites to search
  resultsCount Int     @default(0)
  createdAt   DateTime @default(now())

  @@map("job_searches")
}

enum ApplicationStatus {
  PENDING
  APPLIED
  VIEWED
  INTERVIEW_SCHEDULED
  INTERVIEWED
  OFFER_RECEIVED
  ACCEPTED
  REJECTED
  WITHDRAWN
}

// Indexes for better performance
// @@index([userId])
// @@index([createdAt])
// @@index([status])
// @@index([site, isActive])
// @@index([matchScore])
