# Job Finder Application

A comprehensive job search and application management system with AI-powered resume customization and matching.

## Features

- **Resume Upload & Parsing**: Support PDF, DOC, DOCX formats with AI-powered extraction
- **Job Search & Matching**: Search jobs across platforms with AI-powered relevance scoring
- **Resume Customization**: Automatically tailor resumes to specific job descriptions
- **Cover Letter Generation**: AI-powered cover letter creation
- **Application Submission**: Automated form filling and submission
- **Application Dashboard**: Track applications, statuses, and follow-ups

## Tech Stack

### Backend
- **Framework**: FastAPI (Python)
- **Database**: PostgreSQL with Prisma ORM
- **AI Framework**: LangChain + LangGraph
- **AI Model**: Claude API (Anthropic)
- **Job Scraping**: JobSpy
- **Task Queue**: Celery with Redis

### Frontend
- **Framework**: React + TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **API Client**: Axios + React Query

## Project Structure

```
job_finder/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── db/             # Database connection
│   │   ├── models/         # Pydantic models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── prisma/             # Prisma schema and migrations
│   └── migrations/         # Database migrations
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── types/          # TypeScript types
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── jobspy/                 # Job scraping library
├── uploads/                # File uploads
├── storage/                # Generated files
├── tests/                  # Test files
├── docs/                   # Documentation
├── scripts/                # Utility scripts
└── config/                 # Configuration files
```

## Getting Started

### Prerequisites
- Python 3.10+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+

### Installation

1. Clone the repository
2. Set up the backend (see backend/README.md)
3. Set up the frontend (see frontend/README.md)
4. Configure environment variables
5. Run database migrations
6. Start the services

## Environment Variables

Create `.env` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/job_finder"

# Claude API
ANTHROPIC_API_KEY="your_claude_api_key"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
SECRET_KEY="your_secret_key"

# File Storage
UPLOAD_DIR="./uploads"
STORAGE_DIR="./storage"
```

## Development

### Backend Development
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### Database Management
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# View database
npx prisma studio
```

## API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License
