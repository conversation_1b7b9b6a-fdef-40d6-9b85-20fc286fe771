"""
jobspy.jobboard.exceptions
~~~~~~~~~~~~~~~~~~~

This module contains the set of <PERSON><PERSON><PERSON>' exceptions.
"""


class LinkedInException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with LinkedIn")


class IndeedException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with Indeed")


class ZipRecruiterException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with ZipRecruiter")


class GlassdoorException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with Glass<PERSON>or")


class GoogleJobsException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with Google Jobs")


class BaytException(Exception):
    def __init__(self, message=None):
        super().__init__(message or "An error occurred with <PERSON><PERSON>")

class NaukriException(Exception):
    def __init__(self,message=None):
        super().__init__(message or "An error occurred with <PERSON>ukri")