# Database Configuration (Docker)
DATABASE_URL="postgresql://job_finder_user:job_finder_password@localhost:5432/job_finder"
DATABASE_HOST="localhost"
DATABASE_PORT="5432"
DATABASE_NAME="job_finder"
DATABASE_USER="job_finder_user"
DATABASE_PASSWORD="job_finder_password"

# Claude API Configuration
ANTHROPIC_API_KEY="your_claude_api_key_here"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# JWT Configuration
SECRET_KEY="your_super_secret_jwt_key_here"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage Configuration
UPLOAD_DIR="./uploads"
STORAGE_DIR="./storage"
MAX_FILE_SIZE=10485760  # 10MB in bytes

# Application Configuration
APP_NAME="Job Finder"
APP_VERSION="1.0.0"
DEBUG=true
ENVIRONMENT="development"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"

# Celery Configuration
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"

# JobSpy Configuration
JOBSPY_PROXIES=""  # Comma-separated proxy list
JOBSPY_MAX_RESULTS=50
JOBSPY_DEFAULT_COUNTRY="usa"

# Email Configuration (for notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_TLS=true

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FILE="./logs/app.log"

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Security
BCRYPT_ROUNDS=12
