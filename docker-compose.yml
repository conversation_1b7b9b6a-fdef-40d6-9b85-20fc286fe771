version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: job_finder_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: job_finder
      POSTGRES_USER: job_finder_user
      POSTGRES_PASSWORD: job_finder_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - job_finder_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U job_finder_user -d job_finder"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: job_finder_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - job_finder_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: Redis Commander for Redis GUI
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: job_finder_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - job_finder_network
    depends_on:
      - redis
    profiles:
      - tools

  # Optional: pgAdmin for PostgreSQL GUI
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: job_finder_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - job_finder_network
    depends_on:
      - postgres
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  job_finder_network:
    driver: bridge
