#!/bin/bash

# Docker services management script for Job Finder

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to start services
start_services() {
    print_header "Starting Job Finder Services"
    check_docker
    
    print_status "Starting PostgreSQL and Redis..."
    docker-compose up -d postgres redis
    
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if docker-compose ps postgres | grep -q "healthy"; then
        print_status "✅ PostgreSQL is ready"
    else
        print_warning "PostgreSQL might still be starting up..."
    fi
    
    if docker-compose ps redis | grep -q "healthy"; then
        print_status "✅ Redis is ready"
    else
        print_warning "Redis might still be starting up..."
    fi
    
    print_status "Services started successfully!"
    print_status "PostgreSQL: localhost:5432"
    print_status "Redis: localhost:6379"
}

# Function to start services with tools
start_with_tools() {
    print_header "Starting Job Finder Services with Management Tools"
    check_docker
    
    print_status "Starting all services including pgAdmin and Redis Commander..."
    docker-compose --profile tools up -d
    
    print_status "Waiting for services to be ready..."
    sleep 15
    
    print_status "Services started successfully!"
    print_status "PostgreSQL: localhost:5432"
    print_status "Redis: localhost:6379"
    print_status "pgAdmin: http://localhost:8080 (<EMAIL> / admin123)"
    print_status "Redis Commander: http://localhost:8081"
}

# Function to stop services
stop_services() {
    print_header "Stopping Job Finder Services"
    check_docker
    
    print_status "Stopping all services..."
    docker-compose down
    
    print_status "Services stopped successfully!"
}

# Function to restart services
restart_services() {
    print_header "Restarting Job Finder Services"
    stop_services
    sleep 2
    start_services
}

# Function to show service status
show_status() {
    print_header "Job Finder Services Status"
    check_docker
    
    docker-compose ps
}

# Function to show logs
show_logs() {
    print_header "Job Finder Services Logs"
    check_docker
    
    if [ -n "$1" ]; then
        docker-compose logs -f "$1"
    else
        docker-compose logs -f
    fi
}

# Function to clean up (remove containers and volumes)
cleanup() {
    print_header "Cleaning Up Job Finder Services"
    check_docker
    
    print_warning "This will remove all containers and data volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Removing containers and volumes..."
        docker-compose down -v --remove-orphans
        docker-compose rm -f
        print_status "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to backup database
backup_db() {
    print_header "Backing Up PostgreSQL Database"
    check_docker
    
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    print_status "Creating backup: $BACKUP_FILE"
    docker-compose exec postgres pg_dump -U job_finder_user job_finder > "$BACKUP_FILE"
    
    print_status "Backup created successfully: $BACKUP_FILE"
}

# Function to restore database
restore_db() {
    print_header "Restoring PostgreSQL Database"
    check_docker
    
    if [ -z "$1" ]; then
        print_error "Please provide backup file path"
        print_status "Usage: $0 restore <backup_file.sql>"
        exit 1
    fi
    
    if [ ! -f "$1" ]; then
        print_error "Backup file not found: $1"
        exit 1
    fi
    
    print_warning "This will overwrite the current database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restoring from: $1"
        docker-compose exec -T postgres psql -U job_finder_user job_finder < "$1"
        print_status "Database restored successfully!"
    else
        print_status "Restore cancelled."
    fi
}

# Function to run database shell
db_shell() {
    print_header "Opening PostgreSQL Shell"
    check_docker
    
    print_status "Connecting to PostgreSQL..."
    docker-compose exec postgres psql -U job_finder_user job_finder
}

# Function to run Redis CLI
redis_cli() {
    print_header "Opening Redis CLI"
    check_docker
    
    print_status "Connecting to Redis..."
    docker-compose exec redis redis-cli
}

# Main script logic
case "$1" in
    start)
        start_services
        ;;
    start-tools)
        start_with_tools
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    cleanup)
        cleanup
        ;;
    backup)
        backup_db
        ;;
    restore)
        restore_db "$2"
        ;;
    db)
        db_shell
        ;;
    redis)
        redis_cli
        ;;
    *)
        print_header "Job Finder Docker Services Manager"
        echo "Usage: $0 {start|start-tools|stop|restart|status|logs|cleanup|backup|restore|db|redis}"
        echo ""
        echo "Commands:"
        echo "  start       - Start PostgreSQL and Redis"
        echo "  start-tools - Start all services including pgAdmin and Redis Commander"
        echo "  stop        - Stop all services"
        echo "  restart     - Restart all services"
        echo "  status      - Show service status"
        echo "  logs [service] - Show logs (optionally for specific service)"
        echo "  cleanup     - Remove all containers and volumes"
        echo "  backup      - Backup PostgreSQL database"
        echo "  restore <file> - Restore PostgreSQL database from backup"
        echo "  db          - Open PostgreSQL shell"
        echo "  redis       - Open Redis CLI"
        echo ""
        echo "Examples:"
        echo "  $0 start"
        echo "  $0 logs postgres"
        echo "  $0 backup"
        echo "  $0 restore backup_20231201_120000.sql"
        exit 1
        ;;
esac
