-- Database initialization script for <PERSON> Finder
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create additional indexes for better performance (<PERSON><PERSON><PERSON> will create the main tables)
-- These will be applied after Prisma migrations

-- Function to create indexes after tables exist
CREATE OR REPLACE FUNCTION create_performance_indexes()
RETURNS void AS $$
BEGIN
    -- Check if tables exist before creating indexes
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users') THEN
        -- User indexes
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_created_at ON users("createdAt");
        CREATE INDEX IF NOT EXISTS idx_users_active ON users("isActive");
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'resumes') THEN
        -- Resume indexes
        CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes("userId");
        CREATE INDEX IF NOT EXISTS idx_resumes_created_at ON resumes("createdAt");
        CREATE INDEX IF NOT EXISTS idx_resumes_active ON resumes("isActive");
        CREATE INDEX IF NOT EXISTS idx_resumes_skills_gin ON resumes USING gin(skills);
        CREATE INDEX IF NOT EXISTS idx_resumes_text_search ON resumes USING gin(to_tsvector('english', "rawText"));
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'jobs') THEN
        -- Job indexes
        CREATE INDEX IF NOT EXISTS idx_jobs_site ON jobs(site);
        CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs("createdAt");
        CREATE INDEX IF NOT EXISTS idx_jobs_active ON jobs("isActive");
        CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location);
        CREATE INDEX IF NOT EXISTS idx_jobs_company ON jobs(company);
        CREATE INDEX IF NOT EXISTS idx_jobs_skills_gin ON jobs USING gin(skills);
        CREATE INDEX IF NOT EXISTS idx_jobs_text_search ON jobs USING gin(to_tsvector('english', description));
        CREATE INDEX IF NOT EXISTS idx_jobs_salary ON jobs("salaryMin", "salaryMax");
        CREATE INDEX IF NOT EXISTS idx_jobs_remote ON jobs("isRemote");
        CREATE INDEX IF NOT EXISTS idx_jobs_external_site ON jobs("externalId", site);
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'job_matches') THEN
        -- Job match indexes
        CREATE INDEX IF NOT EXISTS idx_job_matches_user_id ON job_matches("userId");
        CREATE INDEX IF NOT EXISTS idx_job_matches_resume_id ON job_matches("resumeId");
        CREATE INDEX IF NOT EXISTS idx_job_matches_job_id ON job_matches("jobId");
        CREATE INDEX IF NOT EXISTS idx_job_matches_score ON job_matches("matchScore");
        CREATE INDEX IF NOT EXISTS idx_job_matches_bookmarked ON job_matches("isBookmarked");
        CREATE INDEX IF NOT EXISTS idx_job_matches_created_at ON job_matches("createdAt");
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'applications') THEN
        -- Application indexes
        CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications("userId");
        CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
        CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications("createdAt");
        CREATE INDEX IF NOT EXISTS idx_applications_applied_at ON applications("appliedAt");
    END IF;

    RAISE NOTICE 'Performance indexes created successfully';
END;
$$ LANGUAGE plpgsql;

-- Create a function to update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Note: Triggers will be created after Prisma migrations
-- This is just a placeholder for the function

RAISE NOTICE 'Database initialization completed successfully';
